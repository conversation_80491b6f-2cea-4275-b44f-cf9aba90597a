from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def main():
    # 啟動瀏覽器
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()))
    
    try:
        # 開啟網站
        driver.get("https://maps.nlsc.gov.tw/T09/")
        
        # 執行 POST 請求
        driver.execute_script("""
            fetch('https://maps.nlsc.gov.tw/T09/Cookie_setCookie.action', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: ''  // 如果需要請求體，請在這裡添加
            }).then(response => {
                console.log('POST 請求響應:', response.status);
                return response.text();
            }).then(text => {
                console.log('響應內容:', text);
            }).catch(error => {
                console.error('錯誤:', error);
            });
        """)
        
        # 等待一段時間以觀察結果
        import time
        time.sleep(5)
        
    finally:
        # 關閉瀏覽器
        driver.quit()

if __name__ == "__main__":
    main()
