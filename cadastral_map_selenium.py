#!/usr/bin/env python3
"""
使用 Selenium 自動化截取國土測繪圖資服務雲的地籍圖
功能：
1. 開啟 https://maps.nlsc.gov.tw/T09/mapshow.action?In_type=web
2. 使用座標定位到指定位置
3. 開啟地籍圖層
4. 截圖並儲存
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, UnexpectedAlertPresentException
from webdriver_manager.chrome import ChromeDriverManager


class CadastralMapCapture:
    def __init__(self, headless=False):
        """初始化 Selenium WebDriver"""
        self.driver = None
        self.wait = None
        self.headless = headless
        self.setup_driver()
    
    def setup_driver(self):
        """設置 Chrome WebDriver"""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-geolocation")  # 禁用地理位置
        chrome_options.add_argument("--disable-notifications")  # 禁用通知

        # 使用 webdriver-manager 自動管理 ChromeDriver
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.wait = WebDriverWait(self.driver, 10)
    
    def handle_alerts(self):
        """處理可能出現的 alert 彈出視窗"""
        try:
            alert = self.driver.switch_to.alert
            alert_text = alert.text
            print(f"發現 alert: {alert_text}")
            alert.accept()  # 點擊確定
            print("已處理 alert")
            return True
        except:
            return False

    def open_website(self):
        """開啟國土測繪圖資服務雲網站"""
        print("正在開啟國土測繪圖資服務雲...")
        self.driver.get("https://maps.nlsc.gov.tw/T09/mapshow.action?In_type=web")
        time.sleep(3)  # 等待頁面載入

        # 處理可能的 alert
        self.handle_alerts()
    
    def close_popups(self):
        """關閉各種彈出視窗"""
        print("正在關閉彈出視窗...")

        # 處理可能的 alert
        self.handle_alerts()

        # 關閉提醒視窗
        try:
            confirm_btn = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//button[text()='確定']"))
            )
            confirm_btn.click()
            print("已關閉提醒視窗")
            time.sleep(1)
        except TimeoutException:
            print("未找到提醒視窗")

        # 處理可能的 alert
        self.handle_alerts()

        # 關閉系統教學視窗
        try:
            close_btns = self.driver.find_elements(By.XPATH, "//button[text()='Close']")
            for btn in close_btns:
                if btn.is_displayed():
                    btn.click()
                    print("已關閉系統教學視窗")
                    time.sleep(1)
                    break
        except NoSuchElementException:
            print("未找到系統教學視窗")

        # 處理可能的 alert
        self.handle_alerts()

        # 關閉滿意度調查視窗
        try:
            close_btns = self.driver.find_elements(By.XPATH, "//button[text()='Close']")
            for btn in close_btns:
                if btn.is_displayed():
                    btn.click()
                    print("已關閉滿意度調查視窗")
                    time.sleep(1)
                    break
        except NoSuchElementException:
            print("未找到滿意度調查視窗")

        # 最後再處理一次可能的 alert
        self.handle_alerts()
    
    def open_location_search(self):
        """開啟定位查詢功能"""
        print("正在開啟定位查詢...")

        # 處理可能的 alert
        self.handle_alerts()

        try:
            # 直接找到「定位查詢」按鈕
            location_btn = self.driver.find_element(By.XPATH, "//a[text()='定位查詢']")

            # 使用 JavaScript 點擊，避免元素不可交互的問題
            self.driver.execute_script("arguments[0].click();", location_btn)
            time.sleep(2)
            print("定位查詢視窗已開啟")

        except Exception as e:
            print(f"開啟定位查詢失敗: {e}")
            raise
    
    def input_coordinates(self, latitude, longitude):
        """輸入座標並定位"""
        print(f"正在輸入座標: 緯度 {latitude}, 經度 {longitude}")

        try:
            # 點擊座標選項
            coord_tab = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//li[text()='坐標']"))
            )
            self.driver.execute_script("arguments[0].click();", coord_tab)
            time.sleep(1)

            # 等待並輸入經度
            longitude_input = self.wait.until(
                EC.presence_of_element_located((By.XPATH, "//input[@id='longitude' or contains(@name, 'longitude')]"))
            )
            longitude_input.clear()
            longitude_input.send_keys(str(longitude))

            # 輸入緯度
            latitude_input = self.wait.until(
                EC.presence_of_element_located((By.XPATH, "//input[@id='latitude' or contains(@name, 'latitude')]"))
            )
            latitude_input.clear()
            latitude_input.send_keys(str(latitude))

            # 點擊定位按鈕
            locate_btn = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//button[@id='normal_coord_query' or contains(text(), '定位')]"))
            )
            self.driver.execute_script("arguments[0].click();", locate_btn)

            print("座標定位完成")
            time.sleep(3)  # 等待地圖載入

        except (TimeoutException, NoSuchElementException) as e:
            print(f"座標輸入失敗: {e}")
            raise
    
    def close_location_dialog(self):
        """關閉定位查詢對話框"""
        try:
            close_btn = self.driver.find_element(By.XPATH, "//div[@aria-label='定位查詢']//button[text()='Close']")
            close_btn.click()
            time.sleep(1)
            print("定位查詢對話框已關閉")
        except NoSuchElementException:
            print("未找到定位查詢對話框關閉按鈕")
    
    def open_layer_settings(self):
        """開啟圖層設定"""
        print("正在開啟圖層設定...")
        try:
            layer_btn = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//a[text()='圖層設定']"))
            )
            layer_btn.click()
            time.sleep(2)
            print("圖層設定已開啟")
        except TimeoutException:
            print("無法找到圖層設定按鈕")
            raise
    
    def enable_cadastral_layer(self):
        """啟用地籍圖層"""
        print("正在啟用地籍圖層...")
        
        try:
            # 點擊圖層列表
            layer_list_btn = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//div[text()='圖層列表']"))
            )
            layer_list_btn.click()
            time.sleep(2)
            
            # 點擊土地圖層
            land_layer_btn = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//a[text()='土地圖層']"))
            )
            land_layer_btn.click()
            time.sleep(2)
            
            # 點擊地籍圖
            cadastral_btn = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//a[text()='地籍圖(僅供參考)']"))
            )
            cadastral_btn.click()
            time.sleep(2)
            
            # 確認地籍圖使用資訊
            try:
                confirm_btn = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//button[text()='確定']"))
                )
                confirm_btn.click()
                print("地籍圖層已啟用")
                time.sleep(2)
            except TimeoutException:
                print("未找到地籍圖確認按鈕")
            
        except TimeoutException as e:
            print(f"啟用地籍圖層失敗: {e}")
            raise
    
    def close_layer_dialogs(self):
        """關閉圖層相關對話框"""
        try:
            # 關閉圖層列表對話框
            close_btns = self.driver.find_elements(By.XPATH, "//button[text()='Close']")
            for btn in close_btns:
                if btn.is_displayed():
                    btn.click()
                    time.sleep(1)
            print("圖層對話框已關閉")
        except NoSuchElementException:
            print("未找到需要關閉的圖層對話框")
    
    def take_screenshot(self, filename):
        """截圖並保存"""
        print(f"正在截圖並保存為: {filename}")
        
        # 確保 screenshot 目錄存在
        os.makedirs("screenshot", exist_ok=True)
        
        # 截圖
        screenshot_path = os.path.join("screenshot", filename)
        self.driver.save_screenshot(screenshot_path)
        print(f"截圖已保存: {screenshot_path}")
        
        return screenshot_path
    
    def capture_cadastral_map(self, latitude, longitude, filename=None):
        """完整的地籍圖截圖流程"""
        if filename is None:
            filename = f"cadastral_map_{latitude}_{longitude}.png"
        
        try:
            # 1. 開啟網站
            self.open_website()
            
            # 2. 關閉彈出視窗
            self.close_popups()
            
            # 3. 開啟定位查詢
            self.open_location_search()
            
            # 4. 輸入座標並定位
            self.input_coordinates(latitude, longitude)
            
            # 5. 關閉定位對話框
            self.close_location_dialog()
            
            # 6. 開啟圖層設定
            self.open_layer_settings()
            
            # 7. 啟用地籍圖層
            self.enable_cadastral_layer()
            
            # 8. 關閉圖層對話框
            self.close_layer_dialogs()
            
            # 9. 等待地圖完全載入
            time.sleep(5)
            
            # 10. 截圖
            screenshot_path = self.take_screenshot(filename)
            
            print("地籍圖截圖完成！")
            return screenshot_path
            
        except Exception as e:
            print(f"截圖過程中發生錯誤: {e}")
            raise
    
    def close(self):
        """關閉瀏覽器"""
        if self.driver:
            self.driver.quit()
            print("瀏覽器已關閉")


def main():
    """主函數"""
    # 座標設定
    latitude = 22.6044488
    longitude = 120.2989729
    
    # 創建截圖工具實例
    capture = CadastralMapCapture(headless=False)  # 設為 True 可無頭模式運行
    
    try:
        # 執行截圖
        screenshot_path = capture.capture_cadastral_map(latitude, longitude)
        print(f"截圖成功保存至: {screenshot_path}")
        
    except Exception as e:
        print(f"執行失敗: {e}")
    
    finally:
        # 關閉瀏覽器
        capture.close()


if __name__ == "__main__":
    main()
