#!/usr/bin/env python3
"""
簡化版 Selenium 地籍圖截圖腳本
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager


def setup_driver(headless=False):
    """設置 Chrome WebDriver"""
    chrome_options = Options()
    if headless:
        chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-geolocation")
    chrome_options.add_argument("--disable-notifications")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    return driver


def handle_alert(driver):
    """處理 alert 彈出視窗"""
    try:
        alert = driver.switch_to.alert
        alert_text = alert.text
        print(f"發現 alert: {alert_text}")
        alert.accept()
        print("已處理 alert")
        return True
    except:
        return False


def main():
    driver = setup_driver(headless=False)
    wait = WebDriverWait(driver, 10)
    
    try:
        print("1. 開啟網站...")
        driver.get("https://maps.nlsc.gov.tw/T09/mapshow.action?In_type=web")
        time.sleep(3)
        handle_alert(driver)
        
        print("2. 關閉彈出視窗...")
        # 關閉提醒視窗
        try:
            confirm_btn = wait.until(EC.element_to_be_clickable((By.XPATH, "//button[text()='確定']")))
            confirm_btn.click()
            print("已關閉提醒視窗")
            time.sleep(1)
        except TimeoutException:
            print("未找到提醒視窗")
        
        handle_alert(driver)
        
        # 關閉其他彈出視窗
        try:
            close_btns = driver.find_elements(By.XPATH, "//button[text()='Close']")
            for btn in close_btns:
                if btn.is_displayed():
                    btn.click()
                    print("已關閉彈出視窗")
                    time.sleep(1)
        except:
            pass
        
        print("3. 開啟定位查詢...")
        location_btn = driver.find_element(By.XPATH, "//a[text()='定位查詢']")
        driver.execute_script("arguments[0].click();", location_btn)
        time.sleep(2)
        print("定位查詢視窗已開啟")
        
        print("4. 點擊座標選項...")
        coord_tab = wait.until(EC.element_to_be_clickable((By.XPATH, "//li[text()='坐標']")))
        driver.execute_script("arguments[0].click();", coord_tab)
        time.sleep(2)
        
        print("5. 查找輸入框...")
        # 打印所有輸入框
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"找到 {len(inputs)} 個輸入框:")
        for i, inp in enumerate(inputs):
            input_id = inp.get_attribute('id')
            input_name = inp.get_attribute('name')
            input_type = inp.get_attribute('type')
            input_value = inp.get_attribute('value')
            print(f"  {i}: id='{input_id}', name='{input_name}', type='{input_type}', value='{input_value}'")
        
        print("6. 輸入座標...")
        # 嘗試找到經度輸入框
        longitude_inputs = driver.find_elements(By.XPATH, "//input[contains(@id, 'longitude') or contains(@name, 'longitude') or @value='120.959121']")
        if longitude_inputs:
            longitude_input = longitude_inputs[0]
            longitude_input.clear()
            longitude_input.send_keys("120.2989729")
            print("經度輸入完成")
        else:
            print("未找到經度輸入框")
        
        # 嘗試找到緯度輸入框
        latitude_inputs = driver.find_elements(By.XPATH, "//input[contains(@id, 'latitude') or contains(@name, 'latitude') or @value='23.682531']")
        if latitude_inputs:
            latitude_input = latitude_inputs[0]
            latitude_input.clear()
            latitude_input.send_keys("22.6044488")
            print("緯度輸入完成")
        else:
            print("未找到緯度輸入框")
        
        print("7. 點擊定位按鈕...")
        # 查找定位按鈕
        locate_btns = driver.find_elements(By.XPATH, "//button[contains(text(), '定位')]")
        if locate_btns:
            locate_btn = locate_btns[0]
            driver.execute_script("arguments[0].click();", locate_btn)
            print("定位按鈕已點擊")
            time.sleep(5)  # 等待地圖載入
        else:
            print("未找到定位按鈕")
        
        print("8. 關閉定位對話框...")
        try:
            close_btn = driver.find_element(By.XPATH, "//div[@aria-label='定位查詢']//button[text()='Close']")
            close_btn.click()
            time.sleep(1)
        except:
            print("未找到定位對話框關閉按鈕")
        
        print("9. 開啟圖層設定...")
        layer_btn = driver.find_element(By.XPATH, "//a[text()='圖層設定']")
        driver.execute_script("arguments[0].click();", layer_btn)
        time.sleep(2)
        
        print("10. 點擊圖層列表...")
        layer_list_btn = wait.until(EC.element_to_be_clickable((By.XPATH, "//div[text()='圖層列表']")))
        driver.execute_script("arguments[0].click();", layer_list_btn)
        time.sleep(2)
        
        print("11. 點擊土地圖層...")
        land_layer_btn = wait.until(EC.element_to_be_clickable((By.XPATH, "//a[text()='土地圖層']")))
        driver.execute_script("arguments[0].click();", land_layer_btn)
        time.sleep(2)
        
        print("12. 啟用地籍圖層...")
        cadastral_btn = wait.until(EC.element_to_be_clickable((By.XPATH, "//a[text()='地籍圖(僅供參考)']")))
        driver.execute_script("arguments[0].click();", cadastral_btn)
        time.sleep(2)
        
        print("13. 確認地籍圖使用資訊...")
        try:
            confirm_btn = wait.until(EC.element_to_be_clickable((By.XPATH, "//button[text()='確定']")))
            confirm_btn.click()
            time.sleep(2)
        except:
            print("未找到確認按鈕")
        
        print("14. 關閉對話框...")
        close_btns = driver.find_elements(By.XPATH, "//button[text()='Close']")
        for btn in close_btns:
            if btn.is_displayed():
                btn.click()
                time.sleep(1)
        
        print("15. 等待地圖載入...")
        time.sleep(5)
        
        print("16. 截圖...")
        os.makedirs("screenshot", exist_ok=True)
        screenshot_path = "screenshot/cadastral_map_selenium_22.6044488_120.2989729.png"
        driver.save_screenshot(screenshot_path)
        print(f"截圖已保存: {screenshot_path}")
        
        print("完成！")
        
    except Exception as e:
        print(f"執行過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("關閉瀏覽器...")
        driver.quit()


if __name__ == "__main__":
    main()
